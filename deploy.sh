#!/bin/bash

# Exit on error
set -e

# Load environment variables from .env file if it exists
if [ -f .env ]; then
    echo "Loading environment variables from .env file"
    export $(grep -v '^#' .env | xargs)
fi

# Set default values if not provided
DOCKER_IMAGE=${DOCKER_IMAGE:-ghcr.io/yourusername/foxy-chat-nsfw-service:latest}

# Pull the latest image
echo "Pulling the latest Docker image: $DOCKER_IMAGE"
docker-compose -f docker-compose.prod.yml pull

# Stop and remove existing containers
echo "Stopping existing containers"
docker-compose -f docker-compose.prod.yml down || true

# Start the new containers
echo "Starting new containers"
DOCKER_IMAGE=$DOCKER_IMAGE docker-compose -f docker-compose.prod.yml up -d

# Check if the service is healthy
echo "Checking service health"
for i in {1..10}; do
    if curl -s http://localhost:8011/health | grep -q "healthy"; then
        echo "Service is healthy!"
        exit 0
    fi
    echo "Waiting for service to become healthy... ($i/10)"
    sleep 5
done

echo "Service failed to become healthy within the timeout period"
exit 1
