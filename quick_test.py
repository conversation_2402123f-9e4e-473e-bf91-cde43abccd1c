#!/usr/bin/env python3
"""
Quick test script to verify the application starts and responds properly
"""

import requests
import time
import subprocess
import sys
import os
import signal

def test_application():
    """Test the application startup and basic endpoints"""
    print("🚀 Starting quick test of the application...")
    
    server_process = None
    try:
        # Start the server
        print("Starting server...")
        server_process = subprocess.Popen(
            [sys.executable, "app.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        # Wait for server to start
        print("Waiting for server to start...")
        time.sleep(8)
        
        # Check if server is still running
        if server_process.poll() is not None:
            stdout, stderr = server_process.communicate()
            print(f"❌ Server failed to start!")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return False
        
        print("✅ Server started successfully")
        
        # Test health endpoint
        try:
            print("Testing health endpoint...")
            response = requests.get("http://localhost:8010/health", timeout=10)
            if response.status_code == 200:
                print("✅ Health endpoint working")
                print(f"Response: {response.json()}")
            else:
                print(f"❌ Health endpoint failed: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Health endpoint error: {str(e)}")
        
        # Test metrics endpoint
        try:
            print("Testing metrics endpoint...")
            response = requests.get("http://localhost:8010/metrics", timeout=10)
            if response.status_code == 200:
                print("✅ Metrics endpoint working")
            else:
                print(f"❌ Metrics endpoint failed: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Metrics endpoint error: {str(e)}")
        
        # Test docs endpoint
        try:
            print("Testing docs endpoint...")
            response = requests.get("http://localhost:8010/docs", timeout=10)
            if response.status_code == 200:
                print("✅ Docs endpoint working")
            else:
                print(f"❌ Docs endpoint failed: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Docs endpoint error: {str(e)}")
        
        print("✅ Basic functionality test completed")
        return True
        
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False
        
    finally:
        # Clean up server process
        if server_process:
            print("Stopping server...")
            try:
                server_process.terminate()
                server_process.wait(timeout=10)
                print("✅ Server stopped gracefully")
            except subprocess.TimeoutExpired:
                server_process.kill()
                server_process.wait()
                print("⚠️ Server required force kill")
            except Exception as e:
                print(f"❌ Error stopping server: {str(e)}")

if __name__ == "__main__":
    success = test_application()
    if success:
        print("\n🎉 Quick test completed successfully!")
        print("The application appears to be working correctly.")
    else:
        print("\n❌ Quick test failed!")
        print("There may be issues with the application.")
    
    sys.exit(0 if success else 1)
