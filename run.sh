#!/bin/bash

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Install dependencies if needed
if [ "$1" == "--install" ]; then
    echo "Installing dependencies..."
    pip install -r requirements.txt
fi

# Create logs and shared_files directories if they don't exist
mkdir -p logs
mkdir -p shared_files

# Run the application
echo "Starting NSFW Content Detection Service..."
python3 app.py
