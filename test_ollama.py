import asyncio
import time
import statistics
from typing import List, Dict, Any
import ollama
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class OllamaSpeedTest:
    def __init__(self, model_name: str = "hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M"):
        self.model_name = model_name
        self.client = ollama.Client()
        
    def generate_text_sync(self, prompt: str, max_tokens: int = 100) -> Dict[str, Any]:
        """Synchronous text generation"""
        start_time = time.time()
        
        try:
            response = self.client.generate(
                model=self.model_name,
                prompt=prompt,
                options={
                    'num_predict': max_tokens,
                    'temperature': 0.7,
                }
            )
            
            end_time = time.time()
            
            generated_text = response['response']
            tokens_count = len(generated_text.split())
            duration = end_time - start_time
            
            return {
                'prompt': prompt,
                'response': generated_text,
                'tokens_generated': tokens_count,
                'duration_seconds': duration,
                'tokens_per_second': tokens_count / duration if duration > 0 else 0,
                'thread_id': threading.current_thread().ident,
                'success': True
            }
            
        except Exception as e:
            return {
                'prompt': prompt,
                'error': str(e),
                'duration_seconds': time.time() - start_time,
                'success': False
            }
    
    async def generate_text_async(self, prompt: str, max_tokens: int = 100) -> Dict[str, Any]:
        """Asynchronous text generation using asyncio"""
        start_time = time.time()
        
        try:
            # Run sync function in thread pool
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: self.client.generate(
                    model=self.model_name,
                    prompt=prompt,
                    options={
                        'num_predict': max_tokens,
                        'temperature': 0.7,
                    }
                )
            )
            
            end_time = time.time()
            
            generated_text = response['response']
            tokens_count = len(generated_text.split())
            duration = end_time - start_time
            
            return {
                'prompt': prompt,
                'response': generated_text,
                'tokens_generated': tokens_count,
                'duration_seconds': duration,
                'tokens_per_second': tokens_count / duration if duration > 0 else 0,
                'success': True
            }
            
        except Exception as e:
            return {
                'prompt': prompt,
                'error': str(e),
                'duration_seconds': time.time() - start_time,
                'success': False
            }
    
    def test_concurrent_threading(self, prompts: List[str], max_workers: int = 4) -> Dict[str, Any]:
        """Test concurrent generation using ThreadPoolExecutor"""
        print(f"🧵 Testing with ThreadPoolExecutor (max_workers={max_workers})")
        
        start_time = time.time()
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_prompt = {
                executor.submit(self.generate_text_sync, prompt): prompt 
                for prompt in prompts
            }
            
            for future in as_completed(future_to_prompt):
                result = future.result()
                results.append(result)
                
                if result['success']:
                    print(f"✅ Thread {result['thread_id']}: {result['tokens_per_second']:.2f} tokens/s")
                else:
                    print(f"❌ Error: {result['error']}")
        
        total_time = time.time() - start_time
        successful_results = [r for r in results if r['success']]
        
        return {
            'method': 'ThreadPoolExecutor',
            'total_time': total_time,
            'total_requests': len(prompts),
            'successful_requests': len(successful_results),
            'failed_requests': len(prompts) - len(successful_results),
            'avg_tokens_per_second': statistics.mean([r['tokens_per_second'] for r in successful_results]) if successful_results else 0,
            'total_tokens_generated': sum([r['tokens_generated'] for r in successful_results]),
            'requests_per_second': len(prompts) / total_time,
            'results': results
        }
    
    async def test_concurrent_asyncio(self, prompts: List[str]) -> Dict[str, Any]:
        """Test concurrent generation using asyncio"""
        print(f"⚡ Testing with asyncio")
        
        start_time = time.time()
        
        # Create tasks for all prompts
        tasks = [self.generate_text_async(prompt) for prompt in prompts]
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append({
                    'error': str(result),
                    'success': False
                })
            else:
                processed_results.append(result)
                if result['success']:
                    print(f"✅ Async task: {result['tokens_per_second']:.2f} tokens/s")
                else:
                    print(f"❌ Error: {result['error']}")
        
        total_time = time.time() - start_time
        successful_results = [r for r in processed_results if r.get('success', False)]
        
        return {
            'method': 'asyncio',
            'total_time': total_time,
            'total_requests': len(prompts),
            'successful_requests': len(successful_results),
            'failed_requests': len(prompts) - len(successful_results),
            'avg_tokens_per_second': statistics.mean([r['tokens_per_second'] for r in successful_results]) if successful_results else 0,
            'total_tokens_generated': sum([r['tokens_generated'] for r in successful_results]),
            'requests_per_second': len(prompts) / total_time,
            'results': processed_results
        }
    
    def test_sequential(self, prompts: List[str]) -> Dict[str, Any]:
        """Test sequential generation for comparison"""
        print(f"🔄 Testing sequential generation")
        
        start_time = time.time()
        results = []
        
        for i, prompt in enumerate(prompts):
            print(f"Processing request {i+1}/{len(prompts)}")
            result = self.generate_text_sync(prompt)
            results.append(result)
            
            if result['success']:
                print(f"✅ Sequential: {result['tokens_per_second']:.2f} tokens/s")
            else:
                print(f"❌ Error: {result['error']}")
        
        total_time = time.time() - start_time
        successful_results = [r for r in results if r['success']]
        
        return {
            'method': 'sequential',
            'total_time': total_time,
            'total_requests': len(prompts),
            'successful_requests': len(successful_results),
            'failed_requests': len(prompts) - len(successful_results),
            'avg_tokens_per_second': statistics.mean([r['tokens_per_second'] for r in successful_results]) if successful_results else 0,
            'total_tokens_generated': sum([r['tokens_generated'] for r in successful_results]),
            'requests_per_second': len(prompts) / total_time,
            'results': results
        }
    
    def print_comparison(self, results: List[Dict[str, Any]]):
        """Print comparison of different methods"""
        print("\n" + "="*80)
        print("📊 PERFORMANCE COMPARISON")
        print("="*80)
        
        for result in results:
            print(f"\n🔸 Method: {result['method'].upper()}")
            print(f"   Total Time: {result['total_time']:.2f}s")
            print(f"   Successful Requests: {result['successful_requests']}/{result['total_requests']}")
            print(f"   Requests/Second: {result['requests_per_second']:.2f}")
            print(f"   Avg Tokens/Second: {result['avg_tokens_per_second']:.2f}")
            print(f"   Total Tokens Generated: {result['total_tokens_generated']}")
        
        # Find best performing method
        best_method = max(results, key=lambda x: x['requests_per_second'])
        print(f"\n🏆 Best performing method: {best_method['method'].upper()}")
        print(f"   {best_method['requests_per_second']:.2f} requests/second")

# Example usage and test
async def main():
    # Test prompts
    test_prompts = [
        "Explain quantum computing in simple terms.",
        "Write a short story about a robot learning to paint.",
        "What are the benefits of renewable energy?",
        "Describe the process of photosynthesis.",
        "How does machine learning work?",
        "Write a poem about the ocean.",
        "Explain the concept of time dilation.",
        "What is the importance of biodiversity?"
    ]
    
    # Initialize tester (change model name as needed)
    tester = OllamaSpeedTest(model_name="hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M")  # or "mistral", "codellama", etc.
    
    print("🚀 Starting Ollama Speed Test")
    print(f"Model: {tester.model_name}")
    print(f"Test prompts: {len(test_prompts)}")
    print("-" * 50)
    
    # Test different methods
    results = []
    
    # 1. Sequential test
    sequential_result = tester.test_sequential(test_prompts)
    results.append(sequential_result)
    
    print("\n" + "-" * 50)
    
    # 2. Threading test
    threading_result = tester.test_concurrent_threading(test_prompts, max_workers=4)
    results.append(threading_result)
    
    print("\n" + "-" * 50)
    
    # 3. Asyncio test
    asyncio_result = await tester.test_concurrent_asyncio(test_prompts)
    results.append(asyncio_result)
    
    # Print comparison
    tester.print_comparison(results)

if __name__ == "__main__":
    # Run the test
    asyncio.run(main())