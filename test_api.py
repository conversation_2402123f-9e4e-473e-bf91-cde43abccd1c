import asyncio
import aiohttp
import time
import statistics
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pydantic import BaseModel
import threading
import ollama
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI App
app = FastAPI(title="Ollama Load Test API", version="1.0.0")

# Request/Response Models
class GenerateRequest(BaseModel):
    prompt: str
    model: str = "hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M"
    max_tokens: int = 5000
    temperature: float = 0.7
    user_id: int

class GenerateResponse(BaseModel):
    user_id: int
    request_id: str
    prompt: str
    response: str
    model: str
    tokens_generated: int
    processing_time: float
    queue_time: float
    total_time: float
    timestamp: float

@dataclass
class RequestResult:
    user_id: int
    request_id: str
    start_time: float
    queue_start: float
    processing_start: float
    end_time: float
    queue_time: float
    processing_time: float
    total_time: float
    status_code: int
    response_size: int
    tokens_generated: int
    success: bool
    error: Optional[str] = None

# Global variables for tracking
request_counter = 0
active_requests = 0
request_lock = threading.Lock()
completion_times = []

# Ollama client
ollama_client = ollama.Client()

class OllamaFastAPI:
    def __init__(self, max_concurrent_requests: int = 10):
        self.max_concurrent_requests = max_concurrent_requests
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
        self.request_queue = asyncio.Queue()
        self.completion_tracker = []
        
    async def generate_text(self, request: GenerateRequest) -> GenerateResponse:
        """Generate text using Ollama with queue management"""
        global request_counter, active_requests
        
        request_start = time.time()
        
        with request_lock:
            request_counter += 1
            request_id = f"req_{request_counter}_{request.user_id}"
            active_requests += 1
        
        try:
            # Wait for semaphore (queue time)
            async with self.semaphore:
                queue_end = time.time()
                queue_time = queue_end - request_start
                
                # Process with Ollama
                processing_start = time.time()
                
                # Run Ollama in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                
                def ollama_generate():
                    try:
                        response = ollama_client.generate(
                            model=request.model,
                            prompt=request.prompt,
                            options={
                                'num_predict': request.max_tokens,
                                'temperature': request.temperature,
                            }
                        )
                        return response
                    except Exception as e:
                        logger.error(f"Ollama error for user {request.user_id}: {e}")
                        raise e
                
                ollama_response = await loop.run_in_executor(None, ollama_generate)
                
                processing_end = time.time()
                processing_time = processing_end - processing_start
                total_time = processing_end - request_start
                
                # Count tokens (approximate)
                generated_text = ollama_response.get('response', '')
                tokens_generated = len(generated_text.split())
                
                # Track completion
                with request_lock:
                    active_requests -= 1
                    completion_times.append({
                        'user_id': request.user_id,
                        'completion_time': total_time,
                        'timestamp': processing_end
                    })
                
                return GenerateResponse(
                    user_id=request.user_id,
                    request_id=request_id,
                    prompt=request.prompt,
                    response=generated_text,
                    model=request.model,
                    tokens_generated=tokens_generated,
                    processing_time=processing_time,
                    queue_time=queue_time,
                    total_time=total_time,
                    timestamp=processing_end
                )
                
        except Exception as e:
            with request_lock:
                active_requests -= 1
            
            error_time = time.time()
            logger.error(f"Error processing request for user {request.user_id}: {e}")
            
            raise HTTPException(
                status_code=500,
                detail={
                    "error": str(e),
                    "user_id": request.user_id,
                    "request_id": request_id,
                    "total_time": error_time - request_start
                }
            )

# Initialize Ollama FastAPI wrapper
ollama_api = OllamaFastAPI(max_concurrent_requests=20)

# FastAPI Endpoints
@app.get("/")
async def root():
    return {
        "message": "Ollama FastAPI Load Test Server",
        "status": "running",
        "active_requests": active_requests,
        "total_requests": request_counter
    }

@app.post("/generate", response_model=GenerateResponse)
async def generate_endpoint(request: GenerateRequest):
    """Generate text using Ollama"""
    return await ollama_api.generate_text(request)

@app.get("/stats")
async def get_stats():
    """Get current server statistics"""
    with request_lock:
        recent_completions = completion_times[-100:] if completion_times else []
        avg_completion_time = statistics.mean([c['completion_time'] for c in recent_completions]) if recent_completions else 0
        
        return {
            "total_requests": request_counter,
            "active_requests": active_requests,
            "completed_requests": len(completion_times),
            "avg_completion_time": avg_completion_time,
            "recent_completions": len(recent_completions)
        }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test Ollama connection
        test_response = ollama_client.generate(
            model="hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M",
            prompt="test",
            options={'num_predict': 1}
        )
        return {"status": "healthy", "ollama": "connected"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}

# Load Tester Class
class OllamaLoadTester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results: List[RequestResult] = []
        self.milestones = {}
        
    async def make_request(self, session: aiohttp.ClientSession, user_id: int) -> RequestResult:
        """Make a single request to Ollama API"""
        start_time = time.time()
        request_id = f"user_{user_id}_{int(start_time)}"
        
        try:
            payload = {
                "prompt": f"Write a short creative story about user {user_id} discovering something amazing.",
                "model": "hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M",
                "max_tokens": 5000,
                "temperature": 0.7,
                "user_id": user_id
            }
            
            async with session.post(
                f"{self.base_url}/generate",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=120)  # 2 minutes timeout
            ) as response:
                response_data = await response.json()
                end_time = time.time()
                
                if response.status == 200:
                    return RequestResult(
                        user_id=user_id,
                        request_id=request_id,
                        start_time=start_time,
                        queue_start=start_time,
                        processing_start=start_time + response_data.get('queue_time', 0),
                        end_time=end_time,
                        queue_time=response_data.get('queue_time', 0),
                        processing_time=response_data.get('processing_time', 0),
                        total_time=end_time - start_time,
                        status_code=response.status,
                        response_size=len(str(response_data)),
                        tokens_generated=response_data.get('tokens_generated', 0),
                        success=True
                    )
                else:
                    return RequestResult(
                        user_id=user_id,
                        request_id=request_id,
                        start_time=start_time,
                        queue_start=start_time,
                        processing_start=start_time,
                        end_time=end_time,
                        queue_time=0,
                        processing_time=0,
                        total_time=end_time - start_time,
                        status_code=response.status,
                        response_size=0,
                        tokens_generated=0,
                        success=False,
                        error=f"HTTP {response.status}"
                    )
                    
        except Exception as e:
            end_time = time.time()
            return RequestResult(
                user_id=user_id,
                request_id=request_id,
                start_time=start_time,
                queue_start=start_time,
                processing_start=start_time,
                end_time=end_time,
                queue_time=0,
                processing_time=0,
                total_time=end_time - start_time,
                status_code=0,
                response_size=0,
                tokens_generated=0,
                success=False,
                error=str(e)
            )
    
    async def run_load_test(self, num_users: int = 1000) -> Dict[str, Any]:
        """Run load test with specified number of users"""
        print(f"🚀 Starting Ollama Load Test with {num_users} concurrent users")
        print(f"📊 Target: Measure completion times for milestones")
        
        # Setup aiohttp session with optimized settings
        connector = aiohttp.TCPConnector(
            limit=300,
            limit_per_host=100,
            keepalive_timeout=60,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(total=120)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        ) as session:
            
            # Create all tasks
            tasks = [self.make_request(session, user_id) for user_id in range(1, num_users + 1)]
            
            # Start test
            test_start_time = time.time()
            print(f"⏱️  Test started at: {datetime.fromtimestamp(test_start_time).strftime('%H:%M:%S')}")
            
            # Execute all requests concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append(RequestResult(
                        user_id=i + 1,
                        request_id=f"error_{i+1}",
                        start_time=test_start_time,
                        queue_start=test_start_time,
                        processing_start=test_start_time,
                        end_time=time.time(),
                        queue_time=0,
                        processing_time=0,
                        total_time=time.time() - test_start_time,
                        status_code=0,
                        response_size=0,
                        tokens_generated=0,
                        success=False,
                        error=str(result)
                    ))
                else:
                    processed_results.append(result)
            
            test_end_time = time.time()
            self.results = processed_results
            
            return self.analyze_results(test_start_time, test_end_time, num_users)
    
    def analyze_results(self, start_time: float, end_time: float, num_users: int) -> Dict[str, Any]:
        """Analyze test results with milestone tracking"""
        total_duration = end_time - start_time
        successful_requests = [r for r in self.results if r.success]
        failed_requests = [r for r in self.results if not r.success]
        
        # Sort successful requests by completion time
        successful_requests.sort(key=lambda x: x.end_time)
        
        # Calculate milestones
        milestones = {}
        milestone_points = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000]
        
        for milestone in milestone_points:
            if len(successful_requests) >= milestone:
                milestone_completion_time = successful_requests[milestone - 1].end_time - start_time
                milestones[f"first_{milestone}_users"] = milestone_completion_time
                print(f"🎯 First {milestone} users completed in: {milestone_completion_time:.2f}s")
        
        # Calculate performance metrics
        if successful_requests:
            total_times = [r.total_time for r in successful_requests]
            queue_times = [r.queue_time for r in successful_requests]
            processing_times = [r.processing_time for r in successful_requests]
            
            avg_total_time = statistics.mean(total_times)
            avg_queue_time = statistics.mean(queue_times)
            avg_processing_time = statistics.mean(processing_times)
            
            # Percentiles
            total_times_sorted = sorted(total_times)
            p50 = total_times_sorted[int(0.5 * len(total_times_sorted))]
            p95 = total_times_sorted[int(0.95 * len(total_times_sorted))]
            p99 = total_times_sorted[int(0.99 * len(total_times_sorted))]
            
            total_tokens = sum([r.tokens_generated for r in successful_requests])
            tokens_per_second = total_tokens / total_duration
            
        else:
            avg_total_time = avg_queue_time = avg_processing_time = 0
            p50 = p95 = p99 = 0
            total_tokens = tokens_per_second = 0
        
        return {
            'test_config': {
                'total_users': num_users,
                'test_duration': total_duration,
                'start_time': datetime.fromtimestamp(start_time).isoformat(),
                'end_time': datetime.fromtimestamp(end_time).isoformat(),
            },
            'completion_stats': {
                'successful_requests': len(successful_requests),
                'failed_requests': len(failed_requests),
                'success_rate': len(successful_requests) / len(self.results) * 100,
            },
            'milestones': milestones,
            'performance_metrics': {
                'avg_total_time': avg_total_time,
                'avg_queue_time': avg_queue_time,
                'avg_processing_time': avg_processing_time,
                'median_time': p50,
                'p95_time': p95,
                'p99_time': p99,
                'requests_per_second': len(successful_requests) / total_duration,
                'tokens_per_second': tokens_per_second,
                'total_tokens_generated': total_tokens,
            },
            'detailed_results': [asdict(r) for r in self.results]
        }
    
    def print_results(self, results: Dict[str, Any]):
        """Print comprehensive results"""
        print("\n" + "="*80)
        print("📊 OLLAMA FASTAPI LOAD TEST RESULTS")
        print("="*80)
        
        config = results['test_config']
        stats = results['completion_stats']
        milestones = results['milestones']
        metrics = results['performance_metrics']
        
        print(f"🎯 TEST CONFIGURATION:")
        print(f"   Total Users: {config['total_users']}")
        print(f"   Test Duration: {config['test_duration']:.2f}s")
        print(f"   Start Time: {config['start_time']}")
        
        print(f"\n✅ COMPLETION STATISTICS:")
        print(f"   Successful: {stats['successful_requests']}")
        print(f"   Failed: {stats['failed_requests']}")
        print(f"   Success Rate: {stats['success_rate']:.2f}%")
        
        print(f"\n🏁 MILESTONE COMPLETION TIMES:")
        for milestone, time_taken in milestones.items():
            users = milestone.replace('first_', '').replace('_users', '')
            print(f"   First {users} users: {time_taken:.2f}s")
        
        print(f"\n⚡ PERFORMANCE METRICS:")
        print(f"   Avg Total Time: {metrics['avg_total_time']:.3f}s")
        print(f"   Avg Queue Time: {metrics['avg_queue_time']:.3f}s")
        print(f"   Avg Processing Time: {metrics['avg_processing_time']:.3f}s")
        print(f"   Median Response Time: {metrics['median_time']:.3f}s")
        print(f"   95th Percentile: {metrics['p95_time']:.3f}s")
        print(f"   99th Percentile: {metrics['p99_time']:.3f}s")
        print(f"   Requests/Second: {metrics['requests_per_second']:.2f}")
        print(f"   Tokens/Second: {metrics['tokens_per_second']:.2f}")
        print(f"   Total Tokens: {metrics['total_tokens_generated']}")

# Test runner functions
async def run_load_test():
    """Run the load test"""
    tester = OllamaLoadTester()
    
    print("🔧 Starting Ollama load test...")
    print("💡 Make sure:")
    print("   1. Ollama server is running")
    print("   2. hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M model is installed (ollama pull hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M)")
    print("   3. FastAPI server is running on http://localhost:8000")
    
    # Test server health first
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health") as response:
                health_data = await response.json()
                print(f"🏥 Server health: {health_data}")
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Run the load test
    results = await tester.run_load_test(num_users=1000)
    
    # Print and save results
    tester.print_results(results)
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"ollama_load_test_results_{timestamp}.json"
    
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: {results_file}")

def run_server():
    """Run FastAPI server"""
    print("🚀 Starting Ollama FastAPI server...")
    print("📝 Make sure Ollama is running and hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M model is available")
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        run_server()
    elif len(sys.argv) > 1 and sys.argv[1] == "test":
        asyncio.run(run_load_test())
    else:
        print("🎯 Ollama FastAPI Load Tester")
        print("="*50)
        print("Usage:")
        print("  python script.py server  # Run FastAPI server with Ollama")
        print("  python script.py test    # Run 1000 user load test")
        print("\nSetup Steps:")
        print("1. Install: pip install fastapi uvicorn ollama aiohttp")
        print("2. Start Ollama: ollama serve")
        print("3. Pull model: ollama pull hf.co/mradermacher/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored-GGUF:Q4_K_M")
        print("4. Run server: python script.py server")
        print("5. Run test: python script.py test (in another terminal)")