# Python virtual environment
venv/
env/
ENV/
.env

# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Logs
*.log
logs/
nsfw_service.log

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# Docker
.dockerignore

# Temporary files
tmp/
temp/

# Environment variables
.env.local
.env.development
.env.test
.env.production

# Dependency management
pip-log.txt
pip-delete-this-directory.txt

# Hugging Face cache
.huggingface/

# Model files
*.gguf
*.bin
*.pt
*.pth
*.onnx
