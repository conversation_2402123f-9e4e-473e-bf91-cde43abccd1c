version: '3'

services:
  nsfw-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8010:8010"
    volumes:
      - ./logs:/app/logs
      - ./shared_files:/app/shared_files
    environment:
      - OLLAMA_API_URL=http://*************:11434/api/chat
      - OLLAMA_EMBEDDING_URL=http://*************:11434/api/embeddings
      - IMAGE_MODEL=hf.co/likewendy/Qwen2.5-14B-lora-sex-v2-q4_k_m:Q4_K_M
      - EMBEDDING_MODEL=nomic-embed-text
      - HOST=0.0.0.0
      - PORT=8010
      - BASE_URL=${BASE_URL:-http://localhost:8010}
      - SHARED_FILES_DIR=shared_files
      - MAX_TPM=500000
      - MAX_RPM=60
      - LOG_LEVEL=INFO
    extra_hosts:
      - "gen-image-foxy.mirailabs.co:*************"
    restart: unless-stopped
