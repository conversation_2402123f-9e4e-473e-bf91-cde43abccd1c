from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class ModelUsage(BaseModel):
    """Model for tracking model usage"""
    tpm: int = Field(0, description="Tokens per minute")
    rpm: int = Field(0, description="Requests per minute")
    max_tpm: int = Field(..., description="Maximum tokens per minute")
    max_rpm: int = Field(..., description="Maximum requests per minute")


class ModelUsageUpdate(BaseModel):
    """Model for updating model usage limits"""
    max_tpm: Optional[int] = Field(None, description="Maximum tokens per minute")
    max_rpm: Optional[int] = Field(None, description="Maximum requests per minute")


class ModelUsageResponse(BaseModel):
    """Model for model usage response"""
    chat: Dict[str, ModelUsage] = Field({}, description="Chat model usage")
    image: ModelUsage = Field(..., description="Image model usage")
    embedding: ModelUsage = Field(..., description="Embedding model usage")
