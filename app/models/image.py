from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from enum import Enum


class ImageSimilarity(BaseModel):
    image_compare: str = Field(
        default="",
        description="Image compare",
        example=""
    )
    image_red: str = Field(
        default="",
        description="Image read color",
        example=""
    )


class ImageRequest(BaseModel):
    """BaseModel for image generation request"""
    track_id: Optional[str] = Field(
        default="",
        description="ID for tracking the request",
        example=""
    )

    model_id: str = Field(
        ...,
        description="ID of the model to use for generation",
        example="novaOrangeXL_v90.safetensors"
    )

    prompt: str = Field(
        ...,
        min_length=1,
        description="Text prompt describing the desired image",
        example="lazympos, lazypos,((medium-shot)) \n1woman, solo, ((milf)),breast, narrow waist, blue eyes"
    )

    negative_prompt: str = Field(
        default="",
        description="Negative prompt - things to avoid in the generated image",
        example="modern, recent, old, oldest, cartoon, graphic, text, painting"
    )

    width: int = Field(
        default=512,
        ge=64,
        le=2048,
        description="Width of the generated image in pixels"
    )

    height: int = Field(
        default=512,
        ge=64,
        le=2048,
        description="Height of the generated image in pixels"
    )

    styles: Optional[List[str]] = Field(
        default=[],
        description="List of styles to apply to the image"
    )

    seed: int = Field(
        default=-1,
        ge=-1,
        description="Seed for random generation. Use -1 for automatic random seed"
    )

    batch_size: int = Field(
        default=1,
        ge=1,
        le=10,
        description="Number of images to generate in a single batch"
    )

    n_iter: int = Field(
        default=1,
        ge=1,
        le=10,
        description="Number of iterations to run"
    )

    steps: int = Field(
        default=50,
        ge=1,
        le=150,
        description="Number of sampling steps"
    )

    cfg_scale: float = Field(
        default=7.0,
        ge=1.0,
        le=30.0,
        description="Classifier Free Guidance scale - higher values follow prompt more closely"
    )

    eta: float = Field(
        default=0.0,
        ge=0.0,
        le=1.0,
        description="Eta parameter for DDIM sampler (0.0 = deterministic, 1.0 = random)"
    )

    denoising_strength: float = Field(
        default=0.75,
        ge=0.0,
        le=1.0,
        description="Denoising strength (0.0 = no change, 1.0 = complete regeneration)"
    )

    hr_scale: float = Field(
        default=2.0,
        ge=1.0,
        le=4.0,
        description="High resolution upscaling factor"
    )

    scheduler: Optional[str] = Field(
        default="DDIM",
        description="Sampling scheduler algorithm to use"
    )

    base64: str = Field(
        default="no",
        description="Return image as base64 encoded string"
    )

    lora_model: Optional[str] = Field(
        default="",
        description="Name of the LoRA model to use (optional)"
    )

    lora_strength: float = Field(
        default=0.0,
        ge=0.0,
        le=2.0,
        description="Strength of the LoRA model application"
    )

    enable_hr: bool = Field(
        default=False,
        description="Enable high resolution generation"
    )

    hr_second_pass_steps: int = Field(
        default=0,
        ge=0,
        le=150,
        description="Number of steps for the high resolution second pass"
    )
