from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field


class Message(BaseModel):
    """Model for a chat message"""
    role: str
    content: str


class ChatModel(BaseModel):
    """Model for chat request"""
    messages: List[Dict[str, str]]


class ChatResponse(BaseModel):
    """Model for chat response"""
    model: str
    message: Dict[str, str]
    created_at: Optional[str] = None
    done: bool = True
    total_duration: Optional[int] = None
    load_duration: Optional[int] = None
    prompt_eval_count: Optional[int] = None
    prompt_eval_duration: Optional[int] = None
    eval_count: Optional[int] = None
    eval_duration: Optional[int] = None
