from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field


class Message(BaseModel):
    """Model for a message"""
    role: str
    content: str


class EmbeddingRequest(BaseModel):
    """Model for embedding request"""
    messages: List[Dict[str, str]] = Field(..., description="The messages to generate embeddings for")
    model: Optional[str] = Field(None, description="The model to use for embeddings")


class EmbeddingResponse(BaseModel):
    """Model for embedding response"""
    model: str
    embedding: List[float]
    usage: Dict[str, int]
