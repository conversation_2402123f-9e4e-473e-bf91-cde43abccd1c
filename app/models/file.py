from typing import Optional
from pydantic import BaseModel, Field


class FileShareRequest(BaseModel):
    """Model for file sharing request"""
    content: str = Field(..., description="Base64 encoded file content")
    filename: Optional[str] = Field(None, description="Optional filename")
    file_type: Optional[str] = Field(None, description="Optional file type/extension")


class FileShareResponse(BaseModel):
    """Model for file sharing response"""
    url: str = Field(..., description="URL to access the shared file")
    filename: str = Field(..., description="Filename of the shared file")
