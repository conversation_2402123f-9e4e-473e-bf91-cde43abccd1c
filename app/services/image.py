import json
import aiohttp
from typing import List, Dict, Any, Optional
from app.utils.image_torch import ImageUtils
from app.core.logging import logger
from app.core.config import URL_ENDPOINT, MODEL_GEN_IMAGE
import asyncio
import random

MODEL_NAME = "google/vit-base-patch16-224-in21k"
# Đường dẫn thư mục cục bộ để lưu/tải model
LOCAL_MODEL_PATH = "./vit-model-local"


class ImageGenerationManager:
    """Class to manage image generation model usage and rate limiting"""

    def __init__(self, model: str = MODEL_GEN_IMAGE):
        self.model = model
        self.tpm = 0  # tokens per minute
        self.rpm = 0  # requests per minute
        # Use fixed values for image generation
        self.max_tpm = 2500000
        self.max_rpm = 3000

    def set_token(self, token_usage: int) -> None:
        """Increment token and request usage"""
        self.tpm += token_usage
        self.rpm += 1

    def free_token(self, token_usage: int) -> None:
        """Decrement token and request usage"""
        self.tpm -= token_usage
        self.rpm -= 1

    def has_slot(self) -> bool:
        """Check if the model has available capacity"""
        return self.tpm < self.max_tpm and self.rpm < self.max_rpm


class ImageGenerationService:
    """Service for generating images using Ollama API"""

    def __init__(self, image_manager: ImageGenerationManager, image_utils: ImageUtils = None):
        self.image_manager = image_manager
        self.image_utils = image_utils

    async def generate_image(self, messages: List[Dict]) -> Dict[str, Any]:
        """Generate an image using the specified model"""
        if not self.image_manager.has_slot():
            logger.error("Image generation model is at capacity")
            raise RuntimeError("Image generation model is at capacity")

        # Create timeout configuration
        timeout = aiohttp.ClientTimeout(total=300, connect=30)  # 5 minutes total, 30 seconds connect

        async with aiohttp.ClientSession(timeout=timeout) as session:
            payload = json.dumps({
                "model": self.image_manager.model,
                "messages": messages,
                "stream": False
            })

            headers = {
                'Content-Type': 'application/json'
            }

            try:
                url_endpoint_choice = random.choice(URL_ENDPOINT)
                self.image_manager.set_token(1500)
                async with session.post(url_endpoint_choice, data=payload, headers=headers) as response:
                    if response.status == 200:
                        logger.info(f"Created response with model {self.image_manager.model}")
                        result = await response.json()

                        # Update token usage
                        self.image_manager.free_token(1500)

                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"Error: HTTP {response.status}")
                        logger.error(error_text)
                        raise RuntimeError(f"API request failed: {error_text}")
            except asyncio.TimeoutError as e:
                logger.error(f"Timeout error in image generation: {str(e)}")
                raise RuntimeError(f"Image generation request timed out: {str(e)}")
            except aiohttp.ClientError as e:
                logger.error(f"Connection error: {str(e)}")
                raise RuntimeError(f"Connection error: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}")
                raise RuntimeError(f"Unexpected error: {str(e)}")

    def get_usage(self) -> Dict[str, Any]:
        """Get the current usage statistics for the image model"""
        return {
            "tpm": self.image_manager.tpm,
            "rpm": self.image_manager.rpm,
            "max_tpm": self.image_manager.max_tpm,
            "max_rpm": self.image_manager.max_rpm
        }

    async def clc_similarity_image(self, image1, image2):
        """Calculate image similarity asynchronously to prevent blocking"""
        try:
            # Run the CPU-intensive similarity calculation in a thread pool
            # to prevent blocking the event loop
            import concurrent.futures
            import asyncio

            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                similarity = await loop.run_in_executor(
                    executor,
                    self.image_utils.process,
                    image1,
                    image2
                )
                return similarity
        except Exception as e:
            logger.error(f"Error in image similarity calculation: {str(e)}")
            return 1.0  # Return default similarity on error
