import json
import aiohttp
from typing import List, Dict, Any, Optional
from app.utils.image_torch import ImageUtils
from app.core.logging import logger
from app.core.config import URL_ENDPOINT, MODEL_GEN_IMAGE

MODEL_NAME = "google/vit-base-patch16-224-in21k"
# Đường dẫn thư mục cục bộ để lưu/tải model
LOCAL_MODEL_PATH = "./vit-model-local"


class ImageGenerationManager:
    """Class to manage image generation model usage and rate limiting"""

    def __init__(self, model: str = MODEL_GEN_IMAGE):
        self.model = model
        self.tpm = 0  # tokens per minute
        self.rpm = 0  # requests per minute
        # Use fixed values for image generation
        self.max_tpm = 25000
        self.max_rpm = 30

    def set_token(self, token_usage: int) -> None:
        """Increment token and request usage"""
        self.tpm += token_usage
        self.rpm += 1

    def free_token(self, token_usage: int) -> None:
        """Decrement token and request usage"""
        self.tpm -= token_usage
        self.rpm -= 1

    def has_slot(self) -> bool:
        """Check if the model has available capacity"""
        return self.tpm < self.max_tpm and self.rpm < self.max_rpm


class ImageGenerationService:
    """Service for generating images using Ollama API"""

    def __init__(self, image_manager: ImageGenerationManager, image_utils: ImageUtils = None):
        self.image_manager = image_manager
        self.image_utils = image_utils

    async def generate_image(self, messages: List[Dict]) -> Dict[str, Any]:
        """Generate an image using the specified model"""
        if not self.image_manager.has_slot():
            logger.error("Image generation model is at capacity")
            raise RuntimeError("Image generation model is at capacity")

        async with aiohttp.ClientSession() as session:
            payload = json.dumps({
                "model": self.image_manager.model,
                "messages": messages,
                "stream": False
            })

            headers = {
                'Content-Type': 'application/json'
            }

            try:
                async with session.post(URL_ENDPOINT, data=payload, headers=headers) as response:
                    if response.status == 200:
                        logger.info(f"Created response with model {self.image_manager.model}")
                        result = await response.json()

                        # Update token usage
                        if "usage" in result and "total_tokens" in result["usage"]:
                            self.image_manager.set_token(result["usage"]["total_tokens"])
                        else:
                            # If no token usage info, estimate based on message length
                            estimated_tokens = sum(len(m.get("content", "")) for m in messages) // 4
                            self.image_manager.set_token(estimated_tokens)

                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"Error: HTTP {response.status}")
                        logger.error(error_text)
                        raise RuntimeError(f"API request failed: {error_text}")
            except aiohttp.ClientError as e:
                logger.error(f"Connection error: {str(e)}")
                raise RuntimeError(f"Connection error: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}")
                raise RuntimeError(f"Unexpected error: {str(e)}")

    def get_usage(self) -> Dict[str, Any]:
        """Get the current usage statistics for the image model"""
        return {
            "tpm": self.image_manager.tpm,
            "rpm": self.image_manager.rpm,
            "max_tpm": self.image_manager.max_tpm,
            "max_rpm": self.image_manager.max_rpm
        }

    async def clc_similarity_image(self, image1, image2):
        return self.image_utils.process(image1, image2)
