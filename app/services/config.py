from typing import List, Dict, Any
import os
from app.core.logging import logger
from app.core.config import MODEL_LIST, MODEL_GEN_IMAGE, MODEL_EMBEDDING


class ConfigService:
    """Service for managing model configuration"""
    
    def __init__(self):
        # Keep references to the original lists/variables
        self.chat_models = MODEL_LIST
        self.image_model = MODEL_GEN_IMAGE
        self.embedding_model = MODEL_EMBEDDING
    
    def get_model_config(self) -> Dict[str, Any]:
        """Get the current model configuration"""
        return {
            "chat_models": self.chat_models,
            "image_model": self.image_model,
            "embedding_model": self.embedding_model
        }
    
    def update_chat_models(self, operation: str, models: List[str]) -> Dict[str, Any]:
        """Update the chat models list"""
        if operation == "add":
            # Add models that don't already exist
            for model in models:
                if model not in self.chat_models:
                    self.chat_models.append(model)
            logger.info(f"Added models to chat models list: {models}")
        elif operation == "remove":
            # Remove models that exist
            self.chat_models = [m for m in self.chat_models if m not in models]
            logger.info(f"Removed models from chat models list: {models}")
        elif operation == "replace":
            # Replace the entire list
            self.chat_models.clear()
            self.chat_models.extend(models)
            logger.info(f"Replaced chat models list with: {models}")
        else:
            raise ValueError(f"Invalid operation: {operation}. Must be 'add', 'remove', or 'replace'")
        
        return self.get_model_config()
    
    def update_image_model(self, model: str) -> Dict[str, Any]:
        """Update the image generation model"""
        # Update the image model
        global MODEL_GEN_IMAGE
        self.image_model = model
        MODEL_GEN_IMAGE = model
        
        # Update environment variable if needed
        os.environ["IMAGE_MODEL"] = model
        
        logger.info(f"Updated image model to: {model}")
        return self.get_model_config()
    
    def update_embedding_model(self, model: str) -> Dict[str, Any]:
        """Update the text embedding model"""
        # Update the embedding model
        global MODEL_EMBEDDING
        self.embedding_model = model
        MODEL_EMBEDDING = model
        
        # Update environment variable if needed
        os.environ["EMBEDDING_MODEL"] = model
        
        logger.info(f"Updated embedding model to: {model}")
        return self.get_model_config()
