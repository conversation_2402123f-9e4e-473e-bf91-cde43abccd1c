import os
import base64
import uuid
from typing import Dict, Any, Optional

from app.core.logging import logger
from app.core.config import SHARED_FILES_DIR, BASE_URL


class FileService:
    """Service for handling file operations"""

    @staticmethod
    def save_shared_file(content: str, filename: Optional[str] = None, file_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Save a file to the shared files directory

        Args:
            content: Base64 encoded file content
            filename: Optional filename
            file_type: Optional file type/extension

        Returns:
            Dict containing the URL and filename of the shared file
        """
        try:
            # Decode base64 content
            file_content = base64.b64decode(content)

            # Generate a unique filename if not provided
            if not filename:
                unique_id = str(uuid.uuid4())
                extension = f".{file_type}" if file_type else ""
                filename = f"{unique_id}{extension}"

            # Ensure the shared files directory exists
            os.makedirs(SHARED_FILES_DIR, exist_ok=True)

            # Save the file
            file_path = os.path.join(SHARED_FILES_DIR, filename)
            with open(file_path, "wb") as f:
                f.write(file_content)

            # Generate the URL for accessing the file
            file_url = f"{BASE_URL}/share/{filename}"

            logger.info(f"File saved: {filename}")
            return {
                "url": file_url,
                "filename": filename
            }
        except Exception as e:
            logger.error(f"Error saving file: {str(e)}")
            raise RuntimeError(f"Error saving file: {str(e)}")
