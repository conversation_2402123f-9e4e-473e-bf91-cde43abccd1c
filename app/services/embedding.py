import json
import aiohttp
from typing import List, Dict, Any, Optional

from app.core.logging import logger
from app.core.config import EMBEDDING_ENDPOINT, MODEL_EMBEDDING


class EmbeddingManager:
    """Class to manage embedding model usage and rate limiting"""

    def __init__(self, model: str = MODEL_EMBEDDING):
        self.model = model
        self.tpm = 0  # tokens per minute
        self.rpm = 0  # requests per minute
        # Use fixed values for embedding
        self.max_tpm = 100000
        self.max_rpm = 120

    def set_token(self, token_usage: int) -> None:
        """Increment token and request usage"""
        self.tpm += token_usage
        self.rpm += 1

    def free_token(self, token_usage: int) -> None:
        """Decrement token and request usage"""
        self.tpm -= token_usage
        self.rpm -= 1

    def has_slot(self) -> bool:
        """Check if the model has available capacity"""
        return self.tpm < self.max_tpm and self.rpm < self.max_rpm


class EmbeddingService:
    """Service for generating text embeddings"""

    def __init__(self, embedding_manager: EmbeddingManager):
        self.embedding_manager = embedding_manager

    async def generate_embedding(self, messages: List[Dict], model: Optional[str] = None) -> Dict[str, Any]:
        """Generate embeddings for the given messages"""
        if not self.embedding_manager.has_slot():
            logger.error("Embedding model is at capacity")
            raise RuntimeError("Embedding model is at capacity")

        model_name = model if model else self.embedding_manager.model

        # Extract text from messages - use the content of the last user message
        text = ""
        for message in reversed(messages):
            if message.get("role") == "user" and "content" in message:
                text = message["content"]
                break

        if not text:
            logger.error("No valid text found in messages")
            raise ValueError("No valid text found in messages")

        async with aiohttp.ClientSession() as session:
            payload = json.dumps({
                "model": model_name,
                "prompt": text
            })

            headers = {
                'Content-Type': 'application/json'
            }

            try:
                async with session.post(EMBEDDING_ENDPOINT, data=payload, headers=headers) as response:
                    if response.status == 200:
                        logger.info(f"Generated embedding with model {model_name}")
                        result = await response.json()

                        # Update token usage
                        if "usage" in result and "total_tokens" in result["usage"]:
                            self.embedding_manager.set_token(result["usage"]["total_tokens"])

                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"Error: HTTP {response.status}")
                        logger.error(error_text)
                        raise RuntimeError(f"API request failed: {error_text}")
            except aiohttp.ClientError as e:
                logger.error(f"Connection error: {str(e)}")
                raise RuntimeError(f"Connection error: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}")
                raise RuntimeError(f"Unexpected error: {str(e)}")

    def get_usage(self) -> Dict[str, Any]:
        """Get the current usage statistics for the embedding model"""
        return {
            "tpm": self.embedding_manager.tpm,
            "rpm": self.embedding_manager.rpm,
            "max_tpm": self.embedding_manager.max_tpm,
            "max_rpm": self.embedding_manager.max_rpm
        }
