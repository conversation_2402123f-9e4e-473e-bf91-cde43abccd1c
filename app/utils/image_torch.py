import torch
from transformers import pipeline
from PIL import Image
import requests
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import os
from io import BytesIO
import sys


class ImageUtils:
    def __init__(self, model_name: str = "google/vit-base-patch16-224-in21k", model_path: str = "./vit-model-local"):
        self.MODEL_NAME = model_name
        self.MODEL_PATH = model_path
        self.model_vit = None
        self.process_vit = None
        self.device = "cpu"
        self.pipe = None
        self._pipe_initialized = False
        # Don't initialize pipeline in constructor to avoid blocking
        # Initialize lazily when first needed

    def init_pipe(self):
        """Initialize the pipeline lazily to avoid blocking during startup"""
        if not self._pipe_initialized:
            try:
                print("Initializing image processing pipeline...")
                self.pipe = pipeline("image-feature-extraction", model="google/vit-base-patch16-224-in21k")
                self._pipe_initialized = True
                print("Image processing pipeline initialized successfully")
            except Exception as e:
                print(f"Error initializing image processing pipeline: {str(e)}")
                self.pipe = None
                self._pipe_initialized = False
                raise

    def open_image(self, url: str):
        try:
            # Add timeout to prevent hanging requests
            timeout = 30  # 30 seconds timeout for image download
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()  # Raise exception for HTTP errors
            return Image.open(BytesIO(response.content)).convert("RGB")
        except requests.exceptions.Timeout as e:
            print(f"Timeout error downloading image from {url}: {str(e)}")
            return None
        except requests.exceptions.RequestException as e:
            print(f"Request error downloading image from {url}: {str(e)}")
            return None
        except Exception as e:
            print(f"Error processing image from {url}: {str(e)}")
            return None

    def process(self, image_compare, image_red):
        """Process two images and calculate their similarity"""
        try:
            # Initialize pipeline if not already done
            if not self._pipe_initialized:
                self.init_pipe()

            if self.pipe is None:
                print("Image processing pipeline not available, returning default similarity")
                return 1.0

            img1 = self.open_image(image_compare)
            img2 = self.open_image(image_red)

            if img1 is None or img2 is None:
                print("Failed to load one or both images, returning default similarity")
                return 1.0

            # Process images with error handling
            try:
                features1 = self.pipe(img1)
                features2 = self.pipe(img2)
            except Exception as e:
                print(f"Error processing images with pipeline: {str(e)}")
                return 1.0

            vec1 = np.array(features1).reshape(1, -1)
            vec2 = np.array(features2).reshape(1, -1)
            similarity = cosine_similarity(vec1, vec2)[0][0]
            return similarity

        except Exception as e:
            print(f"Error in image similarity calculation: {str(e)}")
            return 1.0

    def cleanup(self):
        """Clean up resources"""
        try:
            if self.pipe is not None:
                # Clear the pipeline to free memory
                del self.pipe
                self.pipe = None
                self._pipe_initialized = False
                print("Image processing pipeline cleaned up")
        except Exception as e:
            print(f"Error during cleanup: {str(e)}")

    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup()
