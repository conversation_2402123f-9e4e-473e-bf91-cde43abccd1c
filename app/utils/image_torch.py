import torch
from transformers import pipeline
from PIL import Image
import requests
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import os
from io import BytesIO
import sys


class ImageUtils:
    def __init__(self, model_name: str = "google/vit-base-patch16-224-in21k", model_path: str = "./vit-model-local"):
        self.MODEL_NAME = model_name
        self.MODEL_PATH = model_path
        self.model_vit = None
        self.process_vit = None
        self.device = "cpu"
        self.pipe = None
        self.init_pipe()

    def init_pipe(self):
        self.pipe = pipeline("image-feature-extraction", model="google/vit-base-patch16-224-in21k")

    def open_image(self, url: str):
        try:
            response = requests.get(url)
            return Image.open(BytesIO(response.content)).convert("RGB")
        except Exception as e:
            return None

    def process(self, image_compare, image_red):
        img1 = self.open_image(image_compare)
        img2 = self.open_image(image_red)
        if img1 is None or img2 is None:
            return 1.0
        features1 = self.pipe(img1)
        features2 = self.pipe(img2)
        vec1 = np.array(features1).reshape(1, -1)
        vec2 = np.array(features2).reshape(1, -1)
        similarity = cosine_similarity(vec1, vec2)[0][0]
        return similarity
