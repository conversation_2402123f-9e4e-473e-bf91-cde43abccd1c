import requests
import base64
import json
import os
import uuid
from dotenv import load_dotenv

load_dotenv()

def convert_base64_to_image(base64_string, output_path, filename=None):
    """
    Chuyển đổi chuỗi base64 thành file ảnh

    Args:
        base64_string (str): Chuỗi base64 của ảnh
        output_path (str): Đ<PERSON>ờng dẫn thư mục lưu ảnh
        filename (str, optional): Tên file, nếu không có sẽ tự động tạo

    Returns:
        str: Đường dẫn file ảnh đã lưu
    """
    try:
        os.makedirs(output_path, exist_ok=True)
        if not filename:
            filename = f"image_{str(uuid.uuid4())}.png"

        # Giải mã base64
        image_data = base64.b64decode(base64_string)

        # Đường dẫn file đầy đủ
        file_path = os.path.join(output_path, filename)

        # Lưu file
        with open(file_path, 'wb') as f:
            f.write(image_data)

        print(f"Đã lưu ảnh: {file_path}")
        return filename

    except Exception as e:
        print(f"Lỗi khi chuyển đổi ảnh: {str(e)}")
        return None


async def process_images_from_url(url, request_body, output_folder="shared_files"):
    """
    Lấy dữ liệu JSON từ URL và chuyển đổi các ảnh base64 thành file

    Args:
        url (str): URL API endpoint
        request_body (dict): Body JSON để gửi POST request
        output_folder (str): Thư mục lưu ảnh

    Returns:
        list: Danh sách đường dẫn các file ảnh đã lưu
    """
    try:
        # Gửi POST request
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.post(url, json=request_body, headers=headers)
        response.raise_for_status()  # Raise exception nếu có lỗi HTTP

        # Parse JSON response
        data = response.json()

        # Kiểm tra có field 'images' không
        if 'images' not in data:
            return []

        images = data['images']
        saved_files = []

        # Chuyển đổi từng ảnh
        for i, base64_image in enumerate(images):
            file_name = convert_base64_to_image(
                base64_image,
                output_folder,
                None
            )
            if file_name:
                saved_files.append(file_name)

        print(f"Đã chuyển đổi thành công {len(saved_files)} ảnh")
        return saved_files

    except requests.exceptions.RequestException as e:
        print(f"Lỗi khi gửi request: {str(e)}")
        return []
    except json.JSONDecodeError as e:
        print(f"Lỗi khi parse JSON: {str(e)}")
        return []
    except Exception as e:
        print(f"Lỗi không xác định: {str(e)}")
        return []


def process_local_json_data(json_data, output_folder="shared_files"):
    """
    Chuyển đổi ảnh từ dữ liệu JSON local

    Args:
        json_data (dict): Dữ liệu JSON chứa field 'images'
        output_folder (str): Thư mục lưu ảnh

    Returns:
        list: Danh sách đường dẫn các file ảnh đã lưu
    """
    try:
        if 'images' not in json_data:
            print("Không tìm thấy field 'images' trong dữ liệu")
            return []

        images = json_data['images']
        saved_files = []

        # Chuyển đổi từng ảnh
        for i, base64_image in enumerate(images):
            filename = convert_base64_to_image(
                base64_image,
                output_folder,
                None
            )
            if filename:
                saved_files.append(filename)
        return saved_files

    except Exception as e:
        print(f"Lỗi khi xử lý dữ liệu: {str(e)}")
        return []


# Ví dụ sử dụng
# if __name__ == "__main__":
#     # Ví dụ 1: Xử lý từ URL với POST request
#     api_url = "https://your-api-endpoint.com/generate-images"
#     request_body = {
#         "prompt": "your prompt here",
#         "other_params": "other values"
#     }

#     # Uncomment để sử dụng
#     # saved_images = process_images_from_url(api_url, request_body)

#     # Ví dụ 2: Xử lý từ dữ liệu JSON có sẵn
#     sample_data = {
#         "images": [
#             "iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAIAAAB7GkOt...",  # Base64 string của ảnh 1
#             "iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAIAAAB7GkOt...",  # Base64 string của ảnh 2
#         ]
#     }

#     # Uncomment để test với dữ liệu mẫu
#     # saved_images = process_local_json_data(sample_data)

#     print("Script đã sẵn sàng để sử dụng!")