import asyncio
import aiohttp
import time
import statistics
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import logging
import os
import random # For random_uuid if vllm.utils is not used

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Helper for random_uuid if vllm.utils is not available ---
def simple_random_uuid() -> str:
    return f"{random.randint(0, 0xFFFFFFFFFFFFFFFF):016x}"

# --- Data Classes (similar to original, but client-focused) ---
@dataclass
class RequestResult:
    user_id: int
    request_id: str # Can be client-generated or server-provided if available
    start_time: float
    end_time: float
    total_time: float # Client-perceived total time
    status_code: int
    response_size: int # Bytes
    tokens_generated: int # Extracted from OpenAI response
    tokens_prompt: int # Extracted from OpenAI response
    model_used: str # Extracted from OpenAI response
    success: bool
    error: Optional[str] = None
    # Server-side queue_time and processing_time are not directly available from standard OpenAI API response
    # We can estimate server processing time roughly if a `X-Process-Time` header is available,
    # or rely on client-side total_time.

# --- Load Tester Class (Adapted for OpenAI API) ---
class OpenAILoadTester:
    def __init__(self, base_url: str, api_key: str = "EMPTY", model_name: str = "DavidAU/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored"):
        self.base_url = base_url.rstrip('/') + "/chat/completions" # Ensure it points to the completions endpoint
        self.api_key = api_key
        self.model_name = model_name
        self.results: List[RequestResult] = []
        
    async def make_request(self, session: aiohttp.ClientSession, user_id: int) -> RequestResult:
        start_time = time.time()
        client_request_id = f"loadtest_user_{user_id}_{simple_random_uuid()}"
        
        payload = {
            "model": self.model_name,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                # More varied prompt for better testing
                {"role": "user", "content": f"Tell me a short, unique story for user {user_id}. The story should be about a brave knight and a mischievous dragon. Story ID: {simple_random_uuid()}"},
            ],
            "temperature": 0.7,
            # "stream": False, # OpenAI client lib handles this, for direct aiohttp, non-streaming is simpler for metrics.
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            async with session.post(
                self.base_url,
                json=payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=180) # Increased timeout
            ) as response:
                end_time = time.time()
                response_text = await response.text()
                
                tokens_generated = 0
                tokens_prompt = 0
                model_used_from_response = self.model_name # Default
                
                if response.status == 200:
                    try:
                        response_data = json.loads(response_text)
                        if response_data.get("usage"):
                            tokens_generated = response_data["usage"].get("completion_tokens", 0)
                            tokens_prompt = response_data["usage"].get("prompt_tokens", 0)
                        if response_data.get("model"):
                            model_used_from_response = response_data["model"]
                        
                        return RequestResult(
                            user_id=user_id,
                            request_id=response_data.get("id", client_request_id), # Use server ID if available
                            start_time=start_time,
                            end_time=end_time,
                            total_time=end_time - start_time,
                            status_code=response.status,
                            response_size=len(response_text.encode('utf-8')),
                            tokens_generated=tokens_generated,
                            tokens_prompt=tokens_prompt,
                            model_used=model_used_from_response,
                            success=True
                        )
                    except json.JSONDecodeError as json_e:
                        logger.error(f"JSONDecodeError for user {user_id} (status {response.status}): {json_e}. Response text: {response_text[:500]}")
                        return RequestResult(
                            user_id=user_id, request_id=client_request_id, start_time=start_time, end_time=end_time,
                            total_time=end_time - start_time, status_code=response.status, response_size=len(response_text.encode('utf-8')),
                            tokens_generated=0, tokens_prompt=0, model_used=self.model_name,
                            success=False, error=f"JSONDecodeError: {json_e}"
                        )
                else:
                    logger.warning(f"Request failed for user {user_id} with status {response.status}: {response_text[:500]}")
                    return RequestResult(
                        user_id=user_id, request_id=client_request_id, start_time=start_time, end_time=end_time,
                        total_time=end_time - start_time, status_code=response.status, response_size=len(response_text.encode('utf-8')),
                        tokens_generated=0, tokens_prompt=0, model_used=self.model_name,
                        success=False, error=f"HTTP {response.status}: {response_text[:200]}"
                    )
                    
        except asyncio.TimeoutError:
            end_time = time.time()
            logger.warning(f"Request timed out for user {user_id}")
            return RequestResult(
                user_id=user_id, request_id=client_request_id, start_time=start_time, end_time=end_time,
                total_time=end_time - start_time, status_code=408, # Request Timeout
                response_size=0, tokens_generated=0, tokens_prompt=0, model_used=self.model_name,
                success=False, error="Request timed out"
            )
        except aiohttp.ClientConnectorError as e:
            end_time = time.time()
            logger.error(f"ClientConnectorError for user {user_id}: {e}")
            return RequestResult(
                user_id=user_id, request_id=client_request_id, start_time=start_time, end_time=end_time,
                total_time=end_time - start_time, status_code=0, # Connection error
                response_size=0, tokens_generated=0, tokens_prompt=0, model_used=self.model_name,
                success=False, error=f"ClientConnectorError: {e}"
            )
        except Exception as e:
            end_time = time.time()
            logger.error(f"Generic error for user {user_id} in make_request: {e}", exc_info=True)
            return RequestResult(
                user_id=user_id, request_id=client_request_id, start_time=start_time, end_time=end_time,
                total_time=end_time - start_time, status_code=0, # Generic error
                response_size=0, tokens_generated=0, tokens_prompt=0, model_used=self.model_name,
                success=False, error=str(e)
            )

    async def run_load_test(self, num_total_requests: int = 100, concurrency_limit: int = 10) -> Dict[str, Any]:
        print(f"🚀 Starting OpenAI API Load Test against {self.base_url}")
        print(f"   Model: {self.model_name}")
        print(f"   Total Requests: {num_total_requests}, Concurrency Limit: {concurrency_limit}")
        
        connector = aiohttp.TCPConnector(
            limit=concurrency_limit,
            limit_per_host=concurrency_limit,
            keepalive_timeout=60,
            enable_cleanup_closed=True
        )
        
        self.results = [] 
        semaphore = asyncio.Semaphore(concurrency_limit)

        async def worker(user_id: int, session: aiohttp.ClientSession):
            async with semaphore:
                result = await self.make_request(session, user_id) # user_id is just an identifier here
                self.results.append(result)

        async with aiohttp.ClientSession(connector=connector) as session: # Timeout is per-request
            tasks = [worker(i, session) for i in range(1, num_total_requests + 1)]
            
            test_start_time = time.time()
            print(f"⏱️ Test started at: {datetime.fromtimestamp(test_start_time).strftime('%Y-%m-%d %H:%M:%S')}")
            
            await asyncio.gather(*tasks)

            test_end_time = time.time()
            print(f"🏁 Test finished at: {datetime.fromtimestamp(test_end_time).strftime('%Y-%m-%d %H:%M:%S')}")
            
            return self.analyze_results(test_start_time, test_end_time, num_total_requests, concurrency_limit)

    def analyze_results(self, start_time: float, end_time: float, num_requests: int, concurrency: int) -> Dict[str, Any]:
        total_duration = end_time - start_time
        successful_requests = [r for r in self.results if r.success]
        failed_requests = [r for r in self.results if not r.success]
        
        successful_requests.sort(key=lambda x: x.end_time)
        
        # --- Milestones (Time to complete N successful requests) ---
        calculated_milestones = {}
        fixed_milestones_counts = [10, 50, 100, 200, 500, 1000, 2000, 5000] 
        # Add dynamic milestones based on num_requests
        dynamic_milestones_ratios = [0.1, 0.25, 0.5, 0.75, 1.0]
        for ratio in dynamic_milestones_ratios:
            count = int(num_requests * ratio)
            if count > 0 and count not in fixed_milestones_counts:
                 fixed_milestones_counts.append(count)
        fixed_milestones_counts = sorted(list(set(m for m in fixed_milestones_counts if m <= num_requests)))


        for count in fixed_milestones_counts:
            if len(successful_requests) >= count:
                milestone_completion_time = successful_requests[count - 1].end_time - start_time
                calculated_milestones[f"first_{count}_ok_reqs"] = round(milestone_completion_time, 2)
        
        # --- Performance Metrics ---
        avg_client_total_time = 0
        p50_time = p95_time = p99_time = 0
        total_tokens_generated = total_tokens_prompt = 0
        
        if successful_requests:
            client_total_times = [r.total_time for r in successful_requests]
            avg_client_total_time = statistics.mean(client_total_times)
            
            client_total_times_sorted = sorted(client_total_times)
            p50_time = statistics.median(client_total_times_sorted) # More robust than direct indexing for median
            if len(client_total_times_sorted) >= 20: # Ensure enough data for percentiles
                p95_time = client_total_times_sorted[int(0.95 * len(client_total_times_sorted))]
                p99_time = client_total_times_sorted[int(0.99 * len(client_total_times_sorted))]
            elif client_total_times_sorted: # fallback for smaller samples
                 p95_time = client_total_times_sorted[-1]
                 p99_time = client_total_times_sorted[-1]


            total_tokens_generated = sum(r.tokens_generated for r in successful_requests)
            total_tokens_prompt = sum(r.tokens_prompt for r in successful_requests)
        
        requests_per_second = len(successful_requests) / total_duration if total_duration > 0 else 0
        tokens_generated_per_second = total_tokens_generated / total_duration if total_duration > 0 else 0

        return {
            'test_config': {
                'target_base_url': self.base_url.replace("/chat/completions",""), # Show the base part
                'model_name': self.model_name,
                'total_requests_attempted': num_requests,
                'concurrency_limit': concurrency,
                'test_duration_sec': round(total_duration, 2),
                'start_time_iso': datetime.fromtimestamp(start_time).isoformat(),
                'end_time_iso': datetime.fromtimestamp(end_time).isoformat(),
            },
            'summary_stats': {
                'successful_requests': len(successful_requests),
                'failed_requests': len(failed_requests),
                'success_rate_percent': (len(successful_requests) / num_requests * 100) if num_requests > 0 else 0,
            },
            'milestones_time_to_N_success_sec': calculated_milestones,
            'performance_metrics_on_success': {
                'avg_request_latency_sec': round(avg_client_total_time, 3),
                'median_request_latency_sec (p50)': round(p50_time, 3),
                'p95_request_latency_sec': round(p95_time, 3),
                'p99_request_latency_sec': round(p99_time, 3),
                'overall_requests_per_second (RPS)': round(requests_per_second, 2),
                'overall_generated_tokens_per_second (TPS_gen)': round(tokens_generated_per_second, 2),
                'total_tokens_generated': total_tokens_generated,
                'total_tokens_prompt': total_tokens_prompt,
                'avg_tokens_generated_per_req': round(total_tokens_generated / len(successful_requests),1) if successful_requests else 0,
            },
             'error_summary': self._summarize_errors(failed_requests)
        }

    def _summarize_errors(self, failed_requests: List[RequestResult]) -> Dict[str, int]:
        error_counts: Dict[str, int] = {}
        for r in failed_requests:
            error_key = r.error or "Unknown Error"
            if r.status_code != 0 and r.status_code != 200: # Prepend status code if available and relevant
                error_key = f"HTTP {r.status_code}: {error_key}"
            error_counts[error_key] = error_counts.get(error_key, 0) + 1
        return error_counts

    def print_results(self, results_data: Dict[str, Any]):
        print("\n" + "="*80)
        print("🚀 OpenAI API LOAD TEST RESULTS")
        print("="*80)
        
        config = results_data['test_config']
        summary = results_data['summary_stats']
        milestones = results_data['milestones_time_to_N_success_sec']
        perf = results_data['performance_metrics_on_success']
        errors = results_data['error_summary']
        
        print(f"\n🎯 TEST CONFIGURATION:")
        print(f"   Target API Base: {config['target_base_url']}")
        print(f"   Model: {config['model_name']}")
        print(f"   Total Requests Attempted: {config['total_requests_attempted']}")
        print(f"   Concurrency Limit: {config['concurrency_limit']}")
        print(f"   Test Duration: {config['test_duration_sec']:.2f}s")
        
        print(f"\n✅ SUMMARY STATISTICS:")
        print(f"   Successful Requests: {summary['successful_requests']}")
        print(f"   Failed Requests: {summary['failed_requests']}")
        print(f"   Success Rate: {summary['success_rate_percent']:.2f}%")
        
        if milestones:
            print(f"\n🏁 MILESTONE COMPLETION TIMES (time to N successful requests):")
            for name, time_taken in milestones.items():
                print(f"   {name.replace('_', ' ')}: {time_taken:.2f}s")
        
        print(f"\n⚡ PERFORMANCE METRICS (based on successful requests):")
        print(f"   Avg Request Latency: {perf['avg_request_latency_sec']:.3f}s")
        print(f"     Latency Percentiles: p50: {perf['median_request_latency_sec (p50)']:.3f}s | p95: {perf['p95_request_latency_sec']:.3f}s | p99: {perf['p99_request_latency_sec']:.3f}s")
        print(f"   Overall System Throughput:")
        print(f"     - Requests/Second (RPS): {perf['overall_requests_per_second (RPS)']:.2f}")
        print(f"     - Generated Tokens/Second (TPS): {perf['overall_generated_tokens_per_second (TPS_gen)']:.2f}")
        print(f"   Token Stats:")
        print(f"     - Total Generated: {perf['total_tokens_generated']} | Total Prompt: {perf['total_tokens_prompt']}")
        print(f"     - Avg Generated per Request: {perf['avg_tokens_generated_per_req']:.1f}")

        if errors:
            print("\n⚠️ ERROR SUMMARY:")
            for error_desc, count in errors.items():
                print(f"   - {error_desc}: {count} occurrences")
        print("="*80)

# --- Main Test Execution ---
async def main():
    # --- Configuration ---
    # These should ideally come from env variables or CLI args
    target_api_base_url = os.getenv("OPENAI_API_BASE", "http://localhost:8000/v1") # Example: "http://*************:8000/v1"
    target_api_key = os.getenv("OPENAI_API_KEY", "EMPTY")
    target_model_name = os.getenv("MODEL_NAME", "DavidAU/Qwen3-8B-192k-Context-6X-Josiefied-Uncensored") # Example: "Qwen/Qwen2-7B-Instruct"
    
    num_requests_str = os.getenv("LOAD_TEST_REQUESTS", "1000")
    concurrency_str = os.getenv("LOAD_TEST_CONCURRENCY", "100")

    try:
        num_requests = int(num_requests_str)
        concurrency = int(concurrency_str)
    except ValueError:
        print("Invalid number for requests or concurrency. Using defaults: 100 requests, 10 concurrency.")
        num_requests = 100
        concurrency = 10
    
    if num_requests <= 0 : num_requests = 100
    if concurrency <= 0: concurrency = 10
    if concurrency > num_requests:
        print(f"Concurrency ({concurrency}) > total requests ({num_requests}). Setting concurrency to {num_requests}.")
        concurrency = num_requests

    print("--- Load Test Configuration ---")
    print(f"Target API Base: {target_api_base_url}")
    print(f"API Key: {'Provided' if target_api_key != 'EMPTY' else 'EMPTY (default)'}")
    print(f"Model: {target_model_name}")
    print(f"Total Requests: {num_requests}")
    print(f"Concurrency: {concurrency}")
    print("-------------------------------")
    
    # Simple health check (optional, but good practice)
    # This is a basic check. A real /health endpoint on the server is better.
    try:
        async with aiohttp.ClientSession() as session:
            # Try to fetch models as a basic connectivity test to the base URL (not completions)
            # This endpoint might vary depending on the OpenAI-compatible server.
            # For vLLM, /v1/models is standard.
            health_check_url = target_api_base_url.rstrip('/') + "/models"
            headers = {"Authorization": f"Bearer {target_api_key}"}
            async with session.get(health_check_url, headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as resp:
                if resp.status == 200:
                    print(f"✅ Connectivity check to {health_check_url} successful (status {resp.status}).")
                    # models_data = await resp.json()
                    # print(f"   Available models (first few): {models_data.get('data', [])[:3]}")
                else:
                    print(f"⚠️ Connectivity check to {health_check_url} returned status {resp.status}. Proceeding with test, but server might be misconfigured or path incorrect.")
    except Exception as e:
        print(f"❌ Failed connectivity check to {target_api_base_url}: {e}. Ensure the server is running and accessible.")
        print(f"   Make sure your OPENAI_API_BASE is set correctly (e.g., http://your_ip:your_port/v1)")
        return

    tester = OpenAILoadTester(
        base_url=target_api_base_url, 
        api_key=target_api_key, 
        model_name=target_model_name
    )
    
    results_data = await tester.run_load_test(
        num_total_requests=num_requests, 
        concurrency_limit=concurrency
    )
    
    tester.print_results(results_data)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_slug = target_model_name.replace("/", "_").replace(":","-") # Sanitize for filename
    results_file = f"openai_load_test_{model_slug}_req{num_requests}_conc{concurrency}_{timestamp}.json"
    
    # For saving, include the detailed per-request results
    results_data['detailed_results'] = [asdict(r) for r in tester.results]
    
    try:
        with open(results_file, "w") as f:
            json.dump(results_data, f, indent=2)
        print(f"\n💾 Detailed results saved to: {results_file}")
    except Exception as e:
        print(f"\n❌ Error saving results to {results_file}: {e}")

if __name__ == "__main__":
    print("🚀 OpenAI API Load Tester Utility")
    print("   This script sends requests to an OpenAI-compatible API endpoint (e.g., a running vLLM server).")
    print("   Configure via environment variables or modify the 'Configuration' section in main().")
    print("   Example Environment Variables:")
    print("     export OPENAI_API_BASE=\"http://your_vllm_ip:8000/v1\"")
    print("     export MODEL_NAME=\"meta-llama/Llama-2-7b-chat-hf\"")
    print("     export LOAD_TEST_REQUESTS=500")
    print("     export LOAD_TEST_CONCURRENCY=50")
    print("     export OPENAI_API_KEY=\"your_optional_api_key_if_needed\" (defaults to 'EMPTY')")
    print("-" * 60)
    
    # Ensure aiohttp is installed
    try:
        import aiohttp
    except ImportError:
        print("ERROR: 'aiohttp' library is not installed. Please install it by running: pip install aiohttp")
        exit(1)
        
    asyncio.run(main())