from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os

from app.core.config import MODEL_LIST, HOST, PORT, SHARED_FILES_DIR
from app.core.logging import logger
from app.api.routes import router, initialize_services
from app.services.ollama import OllamaManager

# Initialize FastAPI app
app = FastAPI(
    title="Swap Ollama API",
    description="API endpoints for managing chat conversations and users",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Replace "*" with specific domains for better security
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],
)

# Initialize model managers
model_managers = [OllamaManager(model) for model in MODEL_LIST]

# Initialize services
initialize_services(model_managers)

# Include API routes
app.include_router(router)

# Ensure the shared files directory exists
os.makedirs(SHARED_FILES_DIR, exist_ok=True)

# Mount the static files directory
app.mount("/share", StaticFiles(directory=SHARED_FILES_DIR), name="shared")

if __name__ == "__main__":
    logger.info(f"Starting server on {HOST}:{PORT}")
    logger.info(f"Shared files directory: {SHARED_FILES_DIR}")
    uvicorn.run(app, host=HOST, port=PORT)
