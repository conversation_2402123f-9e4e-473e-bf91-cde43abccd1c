from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
import signal
import sys
import asyncio
from contextlib import asynccontextmanager

from app.core.config import MODEL_LIST, HOST, PORT, SHARED_FILES_DIR
from app.core.logging import logger
from app.api.routes import router, initialize_services
from app.services.ollama import OllamaManager

# Global variables for cleanup
model_managers = None
app_services = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with proper startup and shutdown"""
    global model_managers, app_services

    # Startup
    logger.info("Starting application...")
    try:
        # Initialize model managers
        model_managers = [OllamaManager(model) for model in MODEL_LIST]

        # Initialize services
        app_services = initialize_services(model_managers)

        logger.info("Application startup completed successfully")
        yield

    except Exception as e:
        logger.error(f"Error during application startup: {str(e)}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down application...")
        try:
            # Cleanup services if they exist
            if app_services:
                # Add cleanup logic for services here
                pass
            logger.info("Application shutdown completed successfully")
        except Exception as e:
            logger.error(f"Error during application shutdown: {str(e)}")

# Initialize FastAPI app with lifespan management
app = FastAPI(
    title="Swap Ollama API",
    description="API endpoints for managing chat conversations and users",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Replace "*" with specific domains for better security
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],
)

# Include API routes
app.include_router(router)

# Ensure the shared files directory exists
os.makedirs(SHARED_FILES_DIR, exist_ok=True)

# Mount the static files directory
app.mount("/share", StaticFiles(directory=SHARED_FILES_DIR), name="shared")

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    sys.exit(0)

if __name__ == "__main__":
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info(f"Starting server on {HOST}:{PORT}")
    logger.info(f"Shared files directory: {SHARED_FILES_DIR}")

    try:
        uvicorn.run(app, host=HOST, port=PORT)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
    finally:
        logger.info("Server shutdown complete")
