# Foxy Chat NSFW Service

A FastAPI-based service that acts as a proxy for Ollama API calls, with load balancing and rate limiting capabilities.

## Features

- Load balancing across multiple Ollama models
- Rate limiting to prevent overloading models
- Image generation endpoint
- Voice generation endpoint
- File sharing functionality
- Health check endpoint
- Structured logging

## Project Structure

```
foxy-chat-nsfw-service/
├── app/                      # Main application package
│   ├── api/                  # API endpoints
│   │   ├── __init__.py
│   │   └── routes.py         # API route handlers
│   ├── core/                 # Core functionality
│   │   ├── __init__.py
│   │   ├── config.py         # Configuration settings
│   │   └── logging.py        # Logging configuration
│   ├── models/               # Data models
│   │   ├── __init__.py
│   │   ├── chat.py           # Chat-related models
│   │   ├── error.py          # Error response models
│   │   ├── file.py           # File sharing models
│   │   ├── config.py         # Configuration models
│   │   └── embedding.py      # Embedding models
│   ├── services/             # Business logic
│   │   ├── __init__.py
│   │   ├── ollama.py         # Ollama service
│   │   ├── image.py          # Image generation service
│   │   ├── embedding.py      # Embedding service
│   │   ├── config.py         # Configuration service
│   │   └── file.py           # File service
│   └── __init__.py
├── logs/                     # Log files directory
├── shared_files/             # Shared files directory
├── .env.example              # Example environment variables
├── app.py                    # Main application entry point
├── docker-compose.yml        # Docker Compose configuration
├── Dockerfile                # Docker configuration
├── requirements.txt          # Project dependencies
├── run.sh                    # Script to run the application
├── setup.sh                  # Setup script
└── test_api.py               # API tests
```

## Installation

### Using setup script

```bash
./setup.sh
```

### Manual installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/foxy-chat-nsfw-service.git
cd foxy-chat-nsfw-service
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create a `.env` file from the example:
```bash
cp .env.example .env
```

## Configuration

The service can be configured using environment variables:

- `OLLAMA_API_URL`: URL for the Ollama API (default: "http://*************:11434/api/chat")
- `OLLAMA_EMBEDDING_URL`: URL for the Ollama embeddings API (default: "http://*************:11434/api/embeddings")
- `HOST`: Host to bind the server to (default: "0.0.0.0")
- `PORT`: Port to run the server on (default: 8010)
- `BASE_URL`: Base URL for generating file URLs (default: "http://localhost:8010")
- `SHARED_FILES_DIR`: Directory for storing shared files (default: "shared_files")
- `MAX_TPM`: Maximum tokens per minute (default: 50000)
- `MAX_RPM`: Maximum requests per minute (default: 60)
- `LOG_LEVEL`: Logging level (default: "INFO")

## Usage

### Starting the Service

```bash
./run.sh
```

Or directly:

```bash
python app.py
```

### Using Docker

```bash
docker-compose up -d
```

### API Endpoints

#### 1. Chat Endpoint

```
POST /chat
```

Request body:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ]
}
```

#### 2. Generate Image Endpoint

```
POST /generate-image
```

Request body:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "A beautiful sunset over the ocean"
    }
  ]
}
```

#### 3. Text Embedding Endpoint

```
POST /embeddings
```

Request body:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Text to generate embeddings for"
    }
  ],
  "model": "nomic-embed-text"  // Optional, defaults to configured model
}
```

Response:
```json
{
  "model": "nomic-embed-text",
  "embedding": [0.123, 0.456, ...],
  "usage": {
    "prompt_tokens": 5,
    "total_tokens": 5
  }
}
```

#### 4. Model Usage Statistics

```
GET /usage
```

Response:
```json
{
  "chat": {
    "hf.co/model1": {
      "tpm": 1000,
      "rpm": 5,
      "max_tpm": 50000,
      "max_rpm": 60
    },
    "hf.co/model2": {
      "tpm": 2000,
      "rpm": 10,
      "max_tpm": 50000,
      "max_rpm": 60
    }
  },
  "image": {
    "tpm": 500,
    "rpm": 2,
    "max_tpm": 25000,
    "max_rpm": 30
  },
  "embedding": {
    "tpm": 300,
    "rpm": 15,
    "max_tpm": 100000,
    "max_rpm": 120
  }
}
```

#### 5. Model Configuration Management

Get current model configuration:
```
GET /models/config
```

Response:
```json
{
  "chat_models": [
    "hf.co/mradermacher/Humanish-Roleplay-Llama-3.1-8B-i1-GGUF:Q5_K_M",
    "hf.co/ReadyArt/Forgotten-Safeword-12B-v4.0-Q5_K_M-GGUF:latest",
    "hf.co/mradermacher/DAN-L3-R1-8B-i1-GGUF:Q6_K"
  ],
  "image_model": "hf.co/likewendy/Qwen2.5-14B-lora-sex-v2-q4_k_m:Q4_K_M",
  "embedding_model": "nomic-embed-text"
}
```

Update chat models:
```
PUT /models/chat
```

Request body:
```json
{
  "operation": "add",  // Options: "add", "remove", "replace"
  "models": ["hf.co/new-model-1", "hf.co/new-model-2"]
}
```

Update image model:
```
PUT /models/image
```

Request body:
```json
{
  "model": "hf.co/new-image-model"
}
```

Update embedding model:
```
PUT /models/embedding
```

Request body:
```json
{
  "model": "new-embedding-model"
}
```

#### 6. Voice Generation Endpoint

```
POST /generate-voice
```

Request body:
```json
{
  "content": "Text to generate voice for",
  "voice_id": "af_heart"  // Optional, defaults to 'af_heart'
}
```

Response:
```json
{
  "filename": "3f7b9a1c-8e5d-4f3e-9a8b-7c8e5d4f3e9a.wav",
  "url": "http://localhost:8010/share/3f7b9a1c-8e5d-4f3e-9a8b-7c8e5d4f3e9a.wav"
}
```

#### 7. File Sharing Endpoint

```
POST /share-file
```

Request body:
```json
{
  "content": "base64_encoded_file_content",
  "filename": "example.txt",  // Optional
  "file_type": "txt"          // Optional
}
```

Response:
```json
{
  "url": "http://localhost:8010/share/example.txt",
  "filename": "example.txt"
}
```

#### 8. Health Check

```
GET /health
```

Response:
```json
{
  "status": "healthy"
}
```

## Documentation

API documentation is available at:
- Swagger UI: `http://localhost:8010/docs`
- ReDoc: `http://localhost:8010/redoc`

## Logging

Logs are saved to `logs/app.log` with rotation at 10 MB and retention for 1 week.

## Testing

Run the test script to verify the service is working correctly:

```bash
python test_api.py
```

## CI/CD Pipeline

This project uses GitHub Actions for continuous integration and deployment.

### CI Pipeline

The CI pipeline runs on every push to the main branch and on pull requests:

1. Builds the application
2. Runs tests
3. Builds a Docker image
4. Pushes the Docker image to GitHub Container Registry (only on main branch)

### CD Pipeline

The CD pipeline runs after a successful CI pipeline on the main branch:

1. Connects to the deployment server via SSH
2. Pulls the latest Docker image
3. Deploys the application using Docker Compose

### Setting Up Deployment

1. Create a `.env` file from the example:
   ```bash
   cp .env.prod.example .env
   ```

2. Edit the `.env` file with your specific configuration

3. Run the deployment script:
   ```bash
   ./deploy.sh
   ```

### Required GitHub Secrets

For the CI/CD pipeline to work, you need to set up the following secrets in your GitHub repository:

- `DEPLOY_HOST`: The hostname or IP address of your deployment server
- `DEPLOY_USERNAME`: The username to use for SSH connection
- `DEPLOY_KEY`: The SSH private key for authentication
